// ignore_for_file: deprecated_member_use

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/chat/domain/entities/chat_entity.dart';
import 'package:echipta/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:shimmer/shimmer.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen>
    with TickerProviderStateMixin {
  final TextEditingController controller = TextEditingController();
  final ScrollController scrollController = ScrollController();
  late AnimationController _sendButtonController;
  late AnimationController _messageAnimationController;
  bool _isTyping = false;
  int? _lastMessageId;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _messageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
  }

  @override
  void dispose() {
    controller.dispose();
    scrollController.dispose();
    _sendButtonController.dispose();
    _messageAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(12))),
        title: Text(
          LocaleKeys.help.tr(),
          style: Theme.of(context)
              .textTheme
              .displaySmall!
              .copyWith(color: AppColors.white),
        ),
      ),
      body: BlocListener<ChatBloc, ChatState>(
        listener: (context, state) {
          // Detect new messages and trigger animation
          if (state.chats.isNotEmpty) {
            final latestMessage = state.chats.first;
            if (_lastMessageId != latestMessage.id) {
              _lastMessageId = latestMessage.id;
              _animateNewMessage();
            }
          }
        },
        child: BlocBuilder<ChatBloc, ChatState>(
          builder: (context, state) {
            if (state.chatStutus.isInProgress && state.chats.isEmpty) {
              return _buildShimmerLoading();
            }

            return Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      NotificationListener<ScrollNotification>(
                        onNotification: (ScrollNotification notification) {
                          if (notification is ScrollUpdateNotification) {
                            final metrics = notification.metrics;
                            // Check if user is at the bottom and trying to scroll further
                            if (metrics.pixels >= metrics.maxScrollExtent + 100 && !_isRefreshing) {
                              _triggerRefresh();
                              return true;
                            }
                          }
                          return false;
                        },
                        child: KeyboardDismisser(child: _buildChatList(state)),
                      ),
                      if (_isRefreshing)
                        Positioned(
                          bottom: 20,
                          left: 0,
                          right: 0,
                          child: Center(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Yangilanmoqda...',
                                    style: TextStyle(color: Colors.white, fontSize: 14),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                _buildMessageInput(context),
                Gap(context.padding.bottom)
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: List.generate(8, (index) {
            final isEven = index % 2 == 0;
            return Align(
              alignment: isEven ? Alignment.centerRight : Alignment.centerLeft,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: MediaQuery.of(context).size.width * (isEven ? 0.7 : 0.6),
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20),
                    topRight: const Radius.circular(20),
                    bottomLeft: Radius.circular(isEven ? 20 : 4),
                    bottomRight: Radius.circular(isEven ? 4 : 20),
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildChatList(ChatState state) {
    if (state.chats.isEmpty) {
      return ListView(
        padding: const EdgeInsets.all(20),
        children: const [
          SizedBox(height: 200),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'Hali xabarlar yo\'q',
                  style: TextStyle(color: Colors.grey, fontSize: 16),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return GroupedListView<ChatEntity, DateTime>(
      controller: scrollController,
      elements: state.chats.reversed.toList(),
      padding: const EdgeInsets.all(20),
      groupBy: (element) {
        final date = DateTime.parse(element.created_at);
        return DateTime(date.year, date.month, date.day);
      },
      reverse: true,
      order: GroupedListOrder.DESC,
      groupHeaderBuilder: (element) => _buildDateHeader(element),
      itemBuilder: (context, element) => _buildChatBubble(element),
      physics: const AlwaysScrollableScrollPhysics(),
    );
  }

  Widget _buildDateHeader(ChatEntity element) {
    final date = DateTime.parse(element.created_at);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(date.year, date.month, date.day);

    String dateText;
    if (messageDate == today) {
      dateText = 'Bugun';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      dateText = 'Kecha';
    } else {
      dateText = DateFormat('dd.MM.yyyy').format(date);
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            dateText,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChatBubble(ChatEntity element) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, profileState) {
        final isSentByMe = 1 == element.type;
        final isLatestMessage = _lastMessageId == element.id;

        Widget messageWidget = Align(
          alignment: isSentByMe ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: const Radius.circular(18),
                topRight: const Radius.circular(18),
                bottomLeft: Radius.circular(isSentByMe ? 18 : 4),
                bottomRight: Radius.circular(isSentByMe ? 4 : 18),
              ),
              color: isSentByMe ? AppColors.checkBoxColor : AppColors.primary,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  element.message,
                  style: TextStyle(
                    color: isSentByMe ? AppColors.primary : AppColors.white,
                    fontSize: 15,
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTime(DateTime.parse(element.created_at).toLocal().toString()),
                  style: TextStyle(
                    color: (isSentByMe ? AppColors.primary : AppColors.white)
                        .withOpacity(0.6),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        );

        // Add scale animation for the latest message
        if (isLatestMessage) {
          return ScaleTransition(
            scale: Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: _messageAnimationController,
              curve: Curves.elasticOut,
            )),
            child: FadeTransition(
              opacity: Tween<double>(
                begin: 0.0,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: _messageAnimationController,
                curve: Curves.easeOut,
              )),
              child: messageWidget,
            ),
          );
        }

        return messageWidget;
      },
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: controller,
                onChanged: (value) {
                  setState(() {
                    _isTyping = value.trim().isNotEmpty;
                  });
                  if (value.trim().isNotEmpty) {
                    _sendButtonController.forward();
                  } else {
                    _sendButtonController.reverse();
                  }
                },
                decoration: InputDecoration(
                  hintText: 'Xabar yozing...',
                  hintStyle: TextStyle(color: Colors.grey[500], fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                maxLines: 4,
                minLines: 1,
                textCapitalization: TextCapitalization.sentences,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),
          const SizedBox(width: 8),
          ScaleTransition(
            scale: Tween<double>(begin: 0.8, end: 1.0).animate(
              CurvedAnimation(
                parent: _sendButtonController,
                curve: Curves.elasticOut,
              ),
            ),
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: _isTyping ? AppColors.primary : Colors.grey[400],
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _isTyping ? _sendMessage : null,
                icon: const Icon(Icons.send_rounded, color: Colors.white),
                iconSize: 18,
                padding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (controller.text.trim().isEmpty) return;

    final message = controller.text.trim();

    // Clear input and reset state immediately
    controller.clear();
    setState(() {
      _isTyping = false;
    });
    _sendButtonController.reverse();

    // Send message to backend (no optimistic update)
    context.read<ChatBloc>().add(SendMessageEvent(message: message));
  }

  void _triggerRefresh() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    // Trigger the refresh
    context.read<ChatBloc>().add(GetChatsEvent());

    // Hide the loading indicator after a delay
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isRefreshing = false;
      });
    }
  }



  void _animateNewMessage() {
    _messageAnimationController.reset();
    _messageAnimationController.forward();

    // Auto-scroll to bottom when new message appears
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  String _formatTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return DateFormat('HH:mm').format(dateTime);
    } catch (e) {
      return '';
    }
  }
}
